#!/usr/bin/env python3
"""
部门同步修复演示脚本
演示修复前后的在职人员统计差异
"""

def demo_old_logic():
    """演示修复前的统计逻辑（有问题）"""
    print("=== 修复前的统计逻辑（有问题）===")
    
    # 模拟用户数据
    users = [
        {
            "dept_hierarchy": "重庆至信实业股份有限公司（重庆至信） > 技术研发及应用部 > 软件开发组",
            "status": "正式",
            "company_id": 1
        },
        {
            "dept_hierarchy": "重庆至信实业股份有限公司（重庆至信） > 技术研发及应用部 > 软件开发组", 
            "status": "正式",
            "company_id": 1
        },
        {
            "dept_hierarchy": "重庆至信实业股份有限公司（重庆至信） > 综合管理部",
            "status": "正式", 
            "company_id": 1
        }
    ]
    
    dept_active_user_count = {}
    in_service_statuses = ["试用", "正式", "临时", "试用延期"]
    
    for user in users:
        hierarchy_parts = [part.strip() for part in user["dept_hierarchy"].split(' > ') if part.strip()]
        is_active_user = user["status"] in in_service_statuses
        
        # 原有逻辑：只统计叶子部门的直接人员
        leaf_dept_name = hierarchy_parts[-1]
        leaf_dept_key = f"{leaf_dept_name}#{len(hierarchy_parts)}#{user['company_id'] or 0}"
        
        if leaf_dept_key not in dept_active_user_count:
            dept_active_user_count[leaf_dept_key] = 0
        if is_active_user:
            dept_active_user_count[leaf_dept_key] += 1
    
    print("统计结果：")
    for key, count in dept_active_user_count.items():
        print(f"  {key}: {count}人")
    
    # 检查公司级部门
    company_key = "重庆至信实业股份有限公司（重庆至信）#1#1"
    print(f"\n公司级部门统计: {company_key}")
    print(f"人员数量: {dept_active_user_count.get(company_key, 0)}人")
    print("❌ 问题：公司级部门人员数量为0，会被部门有效性检查过滤掉！")


def demo_new_logic():
    """演示修复后的统计逻辑（正确）"""
    print("\n=== 修复后的统计逻辑（正确）===")
    
    # 模拟用户数据
    users = [
        {
            "dept_hierarchy": "重庆至信实业股份有限公司（重庆至信） > 技术研发及应用部 > 软件开发组",
            "status": "正式",
            "company_id": 1
        },
        {
            "dept_hierarchy": "重庆至信实业股份有限公司（重庆至信） > 技术研发及应用部 > 软件开发组", 
            "status": "正式",
            "company_id": 1
        },
        {
            "dept_hierarchy": "重庆至信实业股份有限公司（重庆至信） > 综合管理部",
            "status": "正式", 
            "company_id": 1
        }
    ]
    
    dept_active_user_count = {}
    in_service_statuses = ["试用", "正式", "临时", "试用延期"]
    
    for user in users:
        hierarchy_parts = [part.strip() for part in user["dept_hierarchy"].split(' > ') if part.strip()]
        is_active_user = user["status"] in in_service_statuses
        
        # 新逻辑：为每一级部门都统计在职人员数量
        for i, dept_name in enumerate(hierarchy_parts):
            dept_key = f"{dept_name}#{i+1}#{user['company_id'] or 0}"
            
            if dept_key not in dept_active_user_count:
                dept_active_user_count[dept_key] = 0
            if is_active_user:
                dept_active_user_count[dept_key] += 1
    
    print("统计结果：")
    for key, count in sorted(dept_active_user_count.items()):
        print(f"  {key}: {count}人")
    
    # 检查公司级部门
    company_key = "重庆至信实业股份有限公司（重庆至信）#1#1"
    print(f"\n公司级部门统计: {company_key}")
    print(f"人员数量: {dept_active_user_count.get(company_key, 0)}人")
    print("✅ 修复：公司级部门正确统计到下属人员，不会被过滤！")


def demo_department_structure():
    """演示修复前后的部门结构差异"""
    print("\n=== 部门结构对比 ===")
    
    print("修复前（公司级部门缺失）：")
    print("腾讯企业邮箱根部门 (ID=1)")
    print("├── (CQYS) 总经理办公室N0")
    print("├── (CQYS) 技术研发及应用部")
    print("├── (CQYS) 综合管理部")
    print("└── ...")
    print("❌ 缺少公司级部门层级")
    
    print("\n修复后（完整层级结构）：")
    print("腾讯企业邮箱根部门 (ID=1)")
    print("├── 重庆至信实业股份有限公司")
    print("│   ├── 总经理办公室N0")
    print("│   ├── 技术研发及应用部")
    print("│   │   └── 软件开发组")
    print("│   ├── 综合管理部")
    print("│   └── ...")
    print("├── 至信实业股份有限公司")
    print("│   └── ...")
    print("└── 其他公司...")
    print("✅ 完整的部门层级结构")


if __name__ == "__main__":
    print("部门同步公司级部门统计修复演示")
    print("=" * 50)
    
    demo_old_logic()
    demo_new_logic()
    demo_department_structure()
    
    print("\n" + "=" * 50)
    print("总结：")
    print("1. 修复前：只统计叶子部门人员，公司级部门被误判为空部门")
    print("2. 修复后：每级部门都统计下属人员，确保公司级部门正确显示")
    print("3. 结果：腾讯企业邮箱中显示完整的部门层级结构")
