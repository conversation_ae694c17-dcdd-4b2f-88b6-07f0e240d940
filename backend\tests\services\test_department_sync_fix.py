"""
测试部门同步中公司级部门统计修复
"""

import pytest
import pytest_asyncio
from unittest.mock import Mock, AsyncMock, patch
from sqlalchemy.orm import Session

from app.services.department_structure_sync import DepartmentStructureSyncService
from app.schemas.department_sync import DepartmentSyncRequest, DepartmentSyncSource
from app.models.ecology_user import EcologyUser


class TestDepartmentSyncFix:
    """测试部门同步修复"""
    
    def setup_method(self):
        """设置测试环境"""
        self.db = Mock(spec=Session)
        self.sync_service = DepartmentStructureSyncService(self.db)
        
        # 模拟泛微用户数据
        self.mock_ecology_users = [
            # 公司A的用户
            Mock(
                spec=EcologyUser,
                dept_hierarchy="重庆至信实业股份有限公司（重庆至信） > 技术研发及应用部 > 软件开发组",
                dept_name="软件开发组",
                dept_id=101,
                company_id=1,
                company_name="重庆至信实业股份有限公司（重庆至信）",
                status="正式"
            ),
            Mock(
                spec=EcologyUser,
                dept_hierarchy="重庆至信实业股份有限公司（重庆至信） > 技术研发及应用部 > 软件开发组",
                dept_name="软件开发组",
                dept_id=101,
                company_id=1,
                company_name="重庆至信实业股份有限公司（重庆至信）",
                status="正式"
            ),
            Mock(
                spec=EcologyUser,
                dept_hierarchy="重庆至信实业股份有限公司（重庆至信） > 综合管理部",
                dept_name="综合管理部",
                dept_id=102,
                company_id=1,
                company_name="重庆至信实业股份有限公司（重庆至信）",
                status="正式"
            ),
            # 公司B的用户
            Mock(
                spec=EcologyUser,
                dept_hierarchy="至信实业股份有限公司 > 财务部",
                dept_name="财务部",
                dept_id=201,
                company_id=2,
                company_name="至信实业股份有限公司",
                status="正式"
            ),
        ]
    
    @pytest.mark.asyncio
    async def test_company_department_user_count_fix(self):
        """测试公司级部门人员统计修复"""
        # 创建同步请求
        request = DepartmentSyncRequest(
            source=DepartmentSyncSource.ALL,
            skip_empty_departments=True
        )

        # 模拟获取泛微用户数据
        with patch('app.crud.ecology_user.get_ecology_users') as mock_get_users:
            mock_get_users.return_value = self.mock_ecology_users

            # 调用获取部门数据的方法
            departments = await self.sync_service._get_departments_from_ecology(request)

            # 验证公司级部门被正确包含
            company_depts = [d for d in departments if d.level == 1]
            assert len(company_depts) == 2, "应该有2个公司级部门"

            # 验证公司名称
            company_names = [d.dept_name for d in company_depts]
            assert "重庆至信实业股份有限公司（重庆至信）" in company_names
            assert "至信实业股份有限公司" in company_names

            # 验证各级部门都被包含
            level_counts = {}
            for dept in departments:
                level_counts[dept.level] = level_counts.get(dept.level, 0) + 1

            assert level_counts[1] == 2, "应该有2个一级部门（公司）"
            assert level_counts[2] >= 2, "应该有至少2个二级部门"
            assert level_counts.get(3, 0) >= 1, "应该有至少1个三级部门"
    
    def test_user_count_statistics(self):
        """测试用户统计逻辑"""
        # 模拟部门活跃用户统计
        dept_active_user_count = {}
        in_service_statuses = ["试用", "正式", "临时", "试用延期"]
        
        # 模拟统计过程
        for user in self.mock_ecology_users:
            if not user.dept_hierarchy:
                continue
                
            hierarchy_parts = [part.strip() for part in user.dept_hierarchy.split(' > ') if part.strip()]
            is_active_user = user.status in in_service_statuses
            
            # 为每一级部门都统计在职人员数量
            for i, dept_name in enumerate(hierarchy_parts):
                dept_key = f"{dept_name}#{i+1}#{user.company_id or 0}"
                
                if dept_key not in dept_active_user_count:
                    dept_active_user_count[dept_key] = 0
                if is_active_user:
                    dept_active_user_count[dept_key] += 1
        
        # 验证公司级部门有正确的人员统计
        company_a_key = "重庆至信实业股份有限公司（重庆至信）#1#1"
        company_b_key = "至信实业股份有限公司#1#2"
        
        assert company_a_key in dept_active_user_count, "公司A应该在统计中"
        assert company_b_key in dept_active_user_count, "公司B应该在统计中"
        
        assert dept_active_user_count[company_a_key] == 3, "公司A应该有3个在职人员"
        assert dept_active_user_count[company_b_key] == 1, "公司B应该有1个在职人员"
        
        # 验证二级部门也有正确统计
        tech_dept_key = "技术研发及应用部#2#1"
        assert tech_dept_key in dept_active_user_count, "技术研发部应该在统计中"
        assert dept_active_user_count[tech_dept_key] == 2, "技术研发部应该有2个在职人员"


if __name__ == "__main__":
    pytest.main([__file__])
